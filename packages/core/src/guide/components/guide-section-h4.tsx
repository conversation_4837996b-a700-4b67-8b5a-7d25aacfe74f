import { GuideMode, Line } from "@repo/core/types/data/widget-guide";
import { cn } from "@repo/ui/lib/utils";
import { FC, useMemo } from "react";
import { useGuideContext } from "../context/guide-context";
import {
  SectionH4Provider,
  useSectionH4Context,
} from "../context/section-h4-context";
import { useGuideTreeViewmodel } from "../viewmodels/guide-tree-viewmodel";
import { GuideLine } from "./guide-line";

const SectionH4: FC = () => {
  const { client, guideMode } = useGuideContext();
  const { data, isTalking, isTalked } = useSectionH4Context();
  const root = useGuideTreeViewmodel(data);

  const hasMask = useMemo(() => {
    return (
      client === "stu" &&
      guideMode === GuideMode.follow &&
      !isTalking &&
      !isTalked
    );
  }, [client, guideMode, isTalking, isTalked]);

  return (
    <div
      data-name="section::h4"
      className={cn("w-full", hasMask && "opacity-15")}
    >
      <GuideLine data={root} root={true} />
    </div>
  );
};

export const GuideSectionH4: FC<{
  data: Line[];
}> = ({ data }) => {
  return (
    <SectionH4Provider data={data}>
      <SectionH4 />
    </SectionH4Provider>
  );
};
