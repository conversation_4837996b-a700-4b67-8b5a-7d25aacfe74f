import { Player } from "@remotion/player";
import { Guide } from "@repo/core/guide/guide";
import { GuideWidgetData } from "@repo/core/types/data/widget-guide";
import { cn } from "@repo/ui/lib/utils";
import { FC, useMemo } from "react";
// import localData from "../../../mock/guide-video.json";
import { EmptyTip } from "./_cmp/empty-tip";
import { playbackRateOptions } from "./player-controls";

interface GuidePlayerProps {
  width?: number;
  height?: number;
  className?: string;
  data: GuideWidgetData | string;
  controls?: boolean;
}

const GuidePlayer: FC<GuidePlayerProps> = ({
  width = 1000,
  height = 600,
  className,
  data,
  controls = true,
}) => {
  const guideData = useMemo(() => {
    if (typeof data === "string") {
      return data ? (JSON.parse(data) as GuideWidgetData) : null;
    }
    return data;
  }, [data]);

  if (!guideData) {
    return <EmptyTip texture="视频未生成" />;
  }
  // const guideData = localData as unknown as GuideWidgetData;

  const { avatar } = guideData;

  return (
    <Player
      className={cn("h-[600px] w-[1000px]", className)}
      style={{ width: "100%" }}
      component={Guide}
      inputProps={{ data: guideData, selectable: false }}
      durationInFrames={avatar?.durationInFrames + avatar?.fps} //增加1秒, bugfix: 最后一点语音未播完就结束
      fps={avatar?.fps}
      showPlaybackRateControl={playbackRateOptions}
      playbackRate={1}
      controls={controls} // 这将隐藏整个控制条
      alwaysShowControls
      allowFullscreen={false}
      compositionWidth={width}
      compositionHeight={height}
      acknowledgeRemotionLicense
      errorFallback={(e: { error: { message: string } }) => (
        <span className="text-sm text-red-500">错误: {e.error.message}</span>
      )}
    />
  );
};

export { GuidePlayer };
