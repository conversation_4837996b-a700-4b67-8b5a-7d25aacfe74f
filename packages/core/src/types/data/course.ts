import { GuideWidgetData } from "./widget-guide";
import { VideoWidgetData } from "./widget-video";

interface CourseSummary {
  lessonId: number;
  name: string;
  theme: string;
  total: number;
  widgets: CourseWidgetSummary[];
}

interface CourseWidgetSummary {
  index: number;
  name: string;
  type: WidgetType;
  status: WidgetStatus;
  hidden: number;
}

type WidgetStatus = "locked" | "completed" | "unlocked";

type WidgetDataMap = {
  guide: GuideWidgetData;
  exercise: string;
  video: VideoWidgetData;
  interactive: string;
};

interface CourseWidget<T extends WidgetType = WidgetType> {
  index: number;
  name: string;
  type: T;
  data: WidgetDataMap[T];
}

type WidgetType = "guide" | "exercise" | "video" | "interactive";

export type {
  CourseSummary,
  CourseWidget,
  CourseWidgetSummary,
  WidgetStatus,
  WidgetType,
};
