interface Response<T> {
  code: number;
  data: T;
  message: string;
}

const fetcher = async <T>(
  url: string | URL | globalThis.Request,
  init?: RequestInit
) => {
  try {
    const response = await fetch(url, {
      ...init,
      headers: {
        ...init?.headers,
      },
    });
    if (!response.ok) {
      const error = new Error("网络请求失败");
      // 将额外的信息附加到错误对象上。
      error.message = await response.text();
      throw error;
    }
    const res = (await response.json()) as Response<T>;
    const { code, message, data } = res;

    if (code !== 0) {
      const error = new Error("网络请求失败");
      error.message = `[${code}] ${message}`;
      throw error;
    }
    return data as T;
  } catch (err) {
    if (err instanceof Error) {
      throw err;
    }
    throw new Error("网络请求失败2");
  }
};

export const fetchFile = async <T>(url: string) => {
  try {
    const response = await fetch(url);
    if (!response.ok) {
      throw new Error("获取文件失败");
    }
    const blob = await response.blob();
    const text = await blob.text();
    return JSON.parse(text) as T;
  } catch (err) {
    throw new Error("获取文件失败");
  }
};

/**
 * TODO: 移动到各个apps内实现
 */
export async function get<T>(
  url: string,
  { query }: { query?: Record<string, string> }
) {
  const params = query ? new URLSearchParams(query) : undefined;
  return await fetcher<T>(`${url}?${params?.toString()}`, {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
    },
  });
}

export default fetcher;
