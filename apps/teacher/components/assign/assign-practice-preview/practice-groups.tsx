import { cn } from "@/utils/utils";
import Tabs from "antd-mobile/es/components/tabs";
import {
  QuestionSetGroupItem,
} from "@/types/assign";
import { useMemo } from "react";
import { PracticeGroupItem } from "./practice-group-item";
import { Signal } from "@preact-signals/safe-react";


interface PracticeGroupsProps {
  style?: React.CSSProperties;
  className?: string;
  practiceGroupList?: QuestionSetGroupItem[];
  activeGroup: Signal<number>;
}

export function PracticeGroups({
  style = {},
  className = "",
  practiceGroupList = [],
  activeGroup,
}: PracticeGroupsProps) {
  const tabsCls = `
    [&_.adm-tabs-header]:pb-0
    [&_.adm-tabs-header]:border-b!
    [&_.adm-tabs-header]:border-line-1!

    [&_.adm-tabs-header-mask-left]:bg-linear-to-r!
    [&_.adm-tabs-header-mask-left]:from-white!
    [&_.adm-tabs-header-mask-left]:to-transparent!

    [&_.adm-tabs-header-mask-right]:bg-linear-to-l!
    [&_.adm-tabs-header-mask-right]:from-white!
    [&_.adm-tabs-header-mask-right]:to-transparent!

    [&_.adm-tabs-tab]:text-sm!
    [&_.adm-tabs-tab]:pb-3.25!
    [&_.adm-tabs-tab]:leading-normal!
    [&_.adm-tabs-tab]:font-semibold!
    [&_.adm-tabs-tab]:text-gray-4!
    [&_.adm-tabs-tab]:p-0!
    [&_.adm-tabs-tab]:pb-0!
    [&_.adm-tabs-tab.adm-tabs-tab-active]:text-primary-1!
    [&_.adm-tabs-content]:pt-4.5!
  `;

  const activeLineStyle = {
    // "--fixed-active-line-width": ".75rem",
    "--active-line-color": "#6574FC",
    "--active-line-height": ".125rem",
  };

  const tabs = useMemo(() => {
    const num = practiceGroupList.map(item => item?.questionGroupQuestionList?.length || 0);
    const sum = num.reduce((a, b) => a + b, 0);
    const question = {
        name: `题目 (${sum})`,
        key: 'question',
        list: practiceGroupList,
    }
    return [question];
  }, [practiceGroupList]);

  return (
    <div className={cn("flex flex-col gap-2.5", className)} style={style}>
      <Tabs activeLineMode="auto" className={tabsCls} style={activeLineStyle} stretch={false}>
        {tabs.map((item) => (
          <Tabs.Tab title={item.name} key={item.key}>
            <div className="space-y-4.5">
              {item.list.map((group: QuestionSetGroupItem) => (
                <PracticeGroupItem key={group.questionGroupId} group={group} activeGroup={activeGroup} />
              ))}
            </div>
          </Tabs.Tab>
        ))}
      </Tabs>
    </div>
  );
}
