/**
 * 友盟统计 https://developer.umeng.com/docs/67963/detail/2392290
 * 教师端数据埋点汇总 https://wcng60ba718p.feishu.cn/wiki/Pw1GwQgfEi7TsPk3dgrcysMDnR0?renamingWikiNode=false&sheet=ceLy7M
 */
export * from "./common";
export * from "./assign";
export * from "./course";
export * from "./homework";
export * from "./personal-center";
export * from "./login";
// import { getAppInfo, getDeviceInfo } from "@repo/lib/utils/device";


class UMengAnalytics {
  private static instance: UMengAnalytics;
  private appInfo = null;
  private deviceName = '';

  public static getInstance(): UMengAnalytics {
    if (!UMengAnalytics.instance) {
      UMengAnalytics.instance = new UMengAnalytics();
    }

    return UMengAnalytics.instance;
  }

  /**
   * 当您的页面上添加了多个统计代码时，需要用到本方法绑定需要哪个siteid对应的统计代码来接受API发送的请求。未绑定的siteid将忽略相关请求。
   */
  private setAccount(siteid: number): void {
    window._czc?.push(["_setAccount", siteid]);
  }

  /**
   * 设置是否自动采集页面
   * @description 如果您使用_trackPageview改写了已有页面的URL，那么建议您在CNZZ的JS统计代码执行前先调用_setAutoPageview，将该页面的自动PV统计关闭，防止页面的流量被统计双倍。
   */
  private setAutoPageview(autoPageview: boolean): void {
    window._czc?.push(["_setAutoPageview", autoPageview]);
  }

  /**
   * trackEvent
   * @param category 目前按页面分类
   * @param action 事件名
   * @param label 用于更详细的描述事件
   * @param value 用于填写打分型事件的分值，加载时间型事件的时长，订单型事件的价格（请填写整数数值，如果填写为其他形式(类型)，系统将按0处理，若填写为浮点小数，系统会自动取整，去掉小数点）
   * @description 用于发送页面上按钮等交互元素被触发时的事件统计请求。如视频的"播放、暂停、调整音量"，页面上的"返回顶部"、"赞"、"收藏"等。也可用于发送Flash事件统计请求。
   * trackEvent事件的String字段长度为255，请使用时注意不要太长
   */
  public trackEvent<T>(
    category: UmengCategory,
    action: T,
    label?: Record<string | number, unknown>,
    value?: number
  ): void {
    const labelStr = JSON.stringify({
      appInfo: this.appInfo ?? undefined,
      deviceName: this.deviceName,
      ...label,
    });

    // 打印埋点信息
    console.group(`%c友盟埋点 => ${category} - ${String(action)}`, 'background: #8B5CF6; color: white; padding: 2px 6px; border-radius: 4px; font-weight: bold;');
    console.log('category:', category);
    console.log('action:', action);
    console.log('labelStr:', labelStr);
    console.log('value:', value);
    console.log('params:', { category, action, label: labelStr, value });
    console.groupEnd();

    window._czc?.push(["_trackEvent", category, action, labelStr, value]);
  }

  /**
   * trackPageview
   * @description 用于发送某个URL的PV统计请求，适用于统计AJAX、异步加载页面，友情链接，下载链接的流量。
   */
  private trackPageview(contentUrl: string, referrerUrl?: string): void {
    window._czc?.push(["_trackPageview", contentUrl, referrerUrl]);
  }

  /**
   * @param uuid _setUUid的value最大长度为128位，请设置uuid时，将value的位数控制在128个字符以内。
   * @description 用于上报自定义的用户ID，将U-Web的ID与自己的用户ID关联使用，方便对不同得人群进行分析，更加准确得了解用户的喜好
   */
  private setUUid(uuid: string): void {
    window._czc?.push(["_setUUid", uuid]);
  }
}

declare global {
  interface Window {
    _czc: {
      push: <T>(params: T) => void;
    };
  }
}

/**
 * umeng事件类别 (目前按页面划分)
 */
export enum UmengCategory {
  /**
   * 通用
   */
  COMMON = "通用",
  /**
   * 布置
   */
  ASSIGN = "布置页",
  /**
   * 课程
   */
  COURSE = "课程页",
  /**
   * 作业
   */
  HOMEWORK = "作业页",
  /**
   * 个人中心
   */
  PERSONAL_CENTER = "个人中心",
  /**
   * 登录
   */
  LOGIN = "登录页",
}

/**
 * 友盟统计实例
 */
export const umeng = UMengAnalytics.getInstance();
