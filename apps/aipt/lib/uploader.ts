import { get as courseCenterFetcherGet } from "./course-center-fetcher";
import { get } from "./fetcher";
import { getFilenameFromUrl } from "./utils";

export async function uploadFileToOss(
  fileName: string,
  data: any,
  file: File,
  ossTokenRes?: any
) {
  console.log(fileName, data, file, ossTokenRes);
  const formData = new FormData();
  formData.append("success_action_status", "200");
  formData.append("policy", data.policy);
  formData.append("x-oss-signature", data["x-oss-signature"]);
  formData.append("x-oss-signature-version", data["x-oss-signature-version"]);
  formData.append("x-oss-credential", data["x-oss-credential"]);
  formData.append("x-oss-date", data["x-oss-date"]);
  formData.append("key", data.dir + "/" + ossTokenRes.fileName); // 文件名
  formData.append("x-oss-security-token", data["x-oss-security-token"]);
  formData.append("file", file as unknown as File); // file 必须为最后一个表单域

  const res = await fetch(data.host, {
    method: "POST",
    body: formData,
  });

  if (!res.ok) {
    return {
      code: 1,
      data: null,
    };
  }
  return {
    code: 0,
    data: res,
  };
}

export async function getUploadToken(
  fileName: string,
  type: string,
  extraParams?: Record<string, any>
) {
  const query = { type, fileName, ...extraParams };
  console.log("📤 getUploadToken 调用参数:", query);

  const res: any = await get("/api/v1/upload/token", {
    query,
  });
  return {
    token: res.formData.policyToken,
    url: res.url,
    fileName: getFilenameFromUrl(res.url),
  };
}

//  课程管理的token 获取
export async function getUploadToken2(fileName: string) {
  const res: any = await courseCenterFetcherGet("/api/v1/common/oss/token", {
    query: { type: "1", fileName },
  });
  return {
    ...res,
    token: res.formData.policyToken,
    url: res.url,
  };
}

export async function uploadFile(
  file: File,
  type: string = "1",
  extraParams?: Record<string, any>
) {
  let fileName = file.name;
  if (fileName.includes("+")) {
    fileName = fileName.replace(/\+/g, "");
  }
  const ossTokenRes = await getUploadToken(fileName, type, extraParams);
  const { token, url } = ossTokenRes;
  await uploadFileToOss(fileName, token, file, ossTokenRes);
  return {
    url,
    fileName,
  };
}

export async function uploadFile2(file: File) {
  let fileName = file.name;
  if (fileName.includes("+")) {
    fileName = fileName.replace(/\+/g, "");
  }
  const ossTokenRes = await getUploadToken2(fileName);
  const { token, url } = ossTokenRes;
  await uploadFileToOss(fileName, token, file, ossTokenRes);
  return {
    url,
    fileName,
  };
}
