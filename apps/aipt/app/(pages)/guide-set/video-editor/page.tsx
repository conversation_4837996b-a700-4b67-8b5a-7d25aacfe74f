"use client";

import { EmptyTip } from "@/app/components/panels/empty-tip";
import { get } from "@/lib/fetcher";
import { GuideWidget } from "@/types/guide-widget";
import { useSignal } from "@preact-signals/safe-react";
import { Player, PlayerRef } from "@remotion/player";
import { Guide } from "@repo/core/guide/guide";
import { GuideWidgetData } from "@repo/core/types/data/widget-guide";
import { useRequest } from "ahooks";
import { cloneDeep, isEqual } from "lodash";
import { useSearchParams } from "next/navigation";
import { Suspense, useCallback, useEffect, useRef, useState } from "react";
import { Toaster } from "sonner";
import Controls from "./_cmp/controls";
import Head from "./_cmp/head";
import Panel from "./_cmp/panel";
import Track from "./_cmp/track";
import {
  ActiveNodeVal,
  activeNodeValInt,
  checkActiveNode,
  GuideWidgetDataExt,
  remarkGuideData,
} from "./_helper";
import { VideoEditorProvider } from "./_helper/context";

interface VideoEditorRenderType {
  guideData: GuideWidgetDataExt;
  partData: GuideWidget;
  updateGuideData: (data: GuideWidgetDataExt) => void;
}

const VideoEditorRender: React.FC<VideoEditorRenderType> = ({
  guideData,
  partData,
  updateGuideData,
}) => {
  // video player元素
  const playerRef = useRef<PlayerRef | null>(null);

  // 从传入参数中获取 总帧数、帧率
  const {
    avatar: { durationInFrames, fps },
  } = guideData;

  // 监听播放进度，更新当前播放时长（非拖动时才自动更新）
  const [isPlaying, setIsPlaying] = useState(false);
  const currentFrameSignal = useSignal(0);
  const currentTimeSignal = useSignal(0);

  // 倍速状体
  const [playbackRate, setPlaybackRate] = useState(1);

  // 检查当前时间是否在某个关键帧节点的时间范围内
  // 当前激活的关键帧节点ID
  const [activeNode, setActiveNode] = useState<ActiveNodeVal>(
    cloneDeep(activeNodeValInt)
  );
  const prevActiveNodeRef = useRef<ActiveNodeVal | null>(null);

  const handleKeyDown = useCallback(
    (e: KeyboardEvent) => {
      if (!playerRef.current) return;
      const player = playerRef.current!;

      // 将2s换算为帧
      const stepFrame = fps * 2;

      // 获取当前播放器状态（你可能需要根据实际API调整）
      const currentFrame = player.getCurrentFrame();

      switch (e.key) {
        case "ArrowRight": // 右键 >
          player.seekTo(currentFrame + stepFrame); // 快进2秒
          break;
        case "ArrowLeft": // 左键 <
          player.seekTo(currentFrame - stepFrame); // 快退2秒，确保不小于0
          break;
        case " ": // 空格
          console.log("点击了空格", isPlaying);
          e.preventDefault();
          isPlaying ? player.pause() : player.play(); // 快退2秒，确保不小于0

          break;
      }
    },
    [isPlaying]
  );

  useEffect(() => {
    window.addEventListener("keydown", handleKeyDown);
    return () => window.removeEventListener("keydown", handleKeyDown);
  }, [handleKeyDown]);

  useEffect(() => {
    const player = playerRef.current!;
    player.setVolume(1);
    let running = true;
    function update() {
      if (!running) return;
      const frame = player.getCurrentFrame();
      currentFrameSignal.value = frame;
      currentTimeSignal.value = frame / fps;

      const activeNodeVal = checkActiveNode(frame, guideData);
      if (activeNodeVal && !isEqual(prevActiveNodeRef.current, activeNodeVal)) {
        setActiveNode(activeNodeVal);
        prevActiveNodeRef.current = activeNodeVal;
        console.log("存在不一样，开始更新", activeNode, activeNodeVal);
      }
      requestAnimationFrame(update);
    }
    update();

    player.addEventListener("play", () => setIsPlaying(true));
    player.addEventListener("pause", () => setIsPlaying(false));
    return () => {
      player.removeEventListener("play", () => setIsPlaying(true));
      player.removeEventListener("pause", () => setIsPlaying(false));
      running = false;
    };
  }, [guideData]);

  const params = {
    guideData,
    activeNode,
    playerRef,
    isPlaying,
    playbackRate,
    setPlaybackRate,
    currentFrameSignal,
    currentTimeSignal,
    setActiveNode,
    updateGuideData,
    partData,
  };
  return (
    <VideoEditorProvider {...params}>
      <div className="video-dark flex h-screen w-screen flex-col flex-wrap overflow-hidden bg-black">
        <Head />
        {/* 内容 */}
        <div className="flex w-full flex-1 overflow-hidden">
          {/* 左侧 */}
          <div className="flex flex-1 flex-col overflow-hidden">
            {/* 播放器: 有默认加id的行为,所以加了cloneDeep，防止污染数据 */}
            <div className="playerOutWrap flex flex-1 items-center justify-center overflow-hidden px-8">
              {/* <div className="playerWrap h-full overflow-auto"> */}
              <div className="playerWrap h-full overflow-auto">
                <Player
                  ref={playerRef}
                  style={
                    {
                      // width: playerSize.value.width,
                      // height: playerSize.value.height,
                      // height: "100%",
                      // height: "100%",
                      // width: 1000,
                      // height: 600,
                      // backgroundColor: "rgba(255,255,255,0.2)",
                      // height: playerSize.height,
                      // width: playerSize.width,
                    }
                  }
                  component={Guide} // 播放内容
                  inputProps={{
                    data: cloneDeep(guideData),
                    selectable: false,
                  }}
                  durationInFrames={durationInFrames + fps} //增加1秒, bugfix: 最后一点语音未播完就结束
                  fps={fps}
                  playbackRate={playbackRate} // 倍速
                  controls={false} // 这将隐藏整个控制条
                  alwaysShowControls
                  allowFullscreen={false}
                  compositionWidth={1000}
                  compositionHeight={600}
                  acknowledgeRemotionLicense
                  errorFallback={(e: { error: { message: string } }) => (
                    <span className="text-sm text-red-500">
                      错误: {e.error.message}
                    </span>
                  )}
                />
              </div>
            </div>
            <Track />
            <Controls />
          </div>

          <Panel />
        </div>
        <Toaster position="top-center" />
      </div>
    </VideoEditorProvider>
  );
};

const VideoEditor = () => {
  // 获取 url 参数
  const searchParams = useSearchParams();
  const guideWidgetSetId = searchParams.get("id") || "";
  const guideWidgetId = searchParams.get("guideWidgetId") || "";

  // 获取稿件详情数据
  const url = `/api/v1/guideWidget/info`;
  const query = { guideWidgetId, guideWidgetSetId };
  const { data: partData } = useRequest<GuideWidget, [string]>(() =>
    get<GuideWidget>(url, { query }).then((res) => {
      let newVideoJson;
      if (typeof res.videoJson === "string") {
        newVideoJson = JSON.parse(res.videoJson) as GuideWidgetData;
      } else {
        newVideoJson = res.videoJson;
      }

      res.videoJson = JSON.stringify(newVideoJson);

      return res;
    })
  );

  // 处理一下videoJson这个字段，这个字段才是播放视频的核心字段
  const [guideData, setGuideData] = useState<GuideWidgetDataExt | null>();

  // 当 partData 变化时处理 videoJson
  useEffect(() => {
    if (!partData) return;
    if (typeof partData.videoJson === "string") {
      try {
        const res = JSON.parse(partData.videoJson) as GuideWidgetData;
        const newGuidData = remarkGuideData(res);
        setGuideData(newGuidData);
      } catch {
        setGuideData(null);
      }
    } else {
      setGuideData(partData.videoJson);
    }
  }, [partData]);

  return (
    <>
      {partData ? (
        <>
          {guideData ? (
            <VideoEditorRender
              guideData={guideData}
              partData={partData}
              updateGuideData={setGuideData}
            />
          ) : (
            <EmptyTip texture="视频未生成" />
          )}
        </>
      ) : (
        <div>暂无数据</div>
      )}
    </>
  );
};

const VideoEditorWithSuspense = () => (
  <Suspense fallback={<div>Loading...</div>}>
    <VideoEditor />
  </Suspense>
);

export default VideoEditorWithSuspense;
