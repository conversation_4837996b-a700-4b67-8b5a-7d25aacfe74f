/**
 * 题目选择器
 * 用于选择题目
 */
import NumberSelector, {
  ItemStatus,
  NumberSelectorOption,
  NumberSelectorRef,
} from '@/pages/source-manage/components/NumberSelector';
import {
  forwardRef,
  memo,
  useCallback,
  useEffect,
  useImperativeHandle,
  useRef,
  useState,
} from 'react';
import { useQuestionCreate } from '../../contexts/QuestionCreateContext';
import { useQuestionCreateActions } from '../../hooks/useQuestionCreateActions';
import { QuestionCreateProps } from '../../types';

export interface QuestionSegmentedRef {
  setErrors: (
    errors: Array<{
      id: string;
      idx: number;
      fieldPath: string;
      errorMsg: string;
    }>,
  ) => void;
  // getCurrentValue: () => number;
  getOptions: () => NumberSelectorOption[];
  // getCount: () => number;
  resetToUnvisited: () => void; // 初始化所有题目为未访问状态
  updateQuestionStatus: (id: string, status: ItemStatus, errorMessage?: string) => void;
  removeQuestion: (id: string) => void;
}

// 过期时间-7天
const expiryTime = 60 * 60 * 1000 * 24 * 7;

const QuestionSegmented = forwardRef<QuestionSegmentedRef, QuestionCreateProps>(
  ({ way, data, onBack, onSubmit }, ref) => {
    // 从 Context 获取数据和操作
    const {
      form,
      questionList,
      activeIndex,
      setActiveIndex,
      isSingleQuestion,
      saveQueue,
      setActiveQuestion,
      setAttachFileList,
      localDraft,
      paperId,
    } = useQuestionCreate();

    const numberSelectorRef = useRef<NumberSelectorRef>(null);
    const { handleBlurSave } = useQuestionCreateActions({ way, data, onBack, onSubmit });

    // 简化的选项状态管理 - 直接使用 NumberSelectorOption 格式
    const [options, setOptions] = useState<NumberSelectorOption[]>([]);
    const [isInitialized, setIsInitialized] = useState(false);

    // 初始化选项状态 - 只在首次加载时初始化
    useEffect(() => {
      if (isInitialized) return;
      const unvisitedItems: NumberSelectorOption[] = localDraft.loadWithExpiry(paperId) || [];
      const activeIdx = unvisitedItems.findIndex((item) => item.status === ItemStatus.ACTIVE);
      if (activeIdx !== -1) {
        setActiveIndex(activeIdx);
        // 设置当前活跃题目
        if (questionList && questionList[activeIdx]) {
          setActiveQuestion(questionList[activeIdx]);
        }
      }
      if (unvisitedItems && unvisitedItems.length > 0) {
        setOptions(unvisitedItems);

        setIsInitialized(true);
        return;
      }
      if (questionList && questionList.length > 0) {
        const initialOptions = questionList.map((question, index) => ({
          id: question.questionId,
          status: index === 0 ? ItemStatus.ACTIVE : ItemStatus.UNVISITED,
          errorMessage: undefined,
        }));
        setActiveIndex(0);
        setOptions(initialOptions);
        // 保存进度到本地
        localDraft.saveWithExpiry(paperId, initialOptions, expiryTime);
        setIsInitialized(true);
      }
    }, [questionList, isInitialized]);

    // 更新问题状态
    const updateQuestionStatus = useCallback(
      (id: string, status: ItemStatus, errorMessage?: string) => {
        setOptions((prev) => {
          const newOptions = prev.map((item) =>
            item.id === id
              ? {
                  ...item,
                  status,
                  errorMessage: status === ItemStatus.ERROR ? errorMessage : undefined,
                }
              : item,
          );
          // 保存进度到本地
          localDraft.saveWithExpiry(paperId, newOptions, expiryTime);
          return newOptions;
        });
      },
      [paperId, localDraft],
    );

    // 批量设置错误状态
    const setErrors = useCallback(
      (
        errors: Array<{
          id: string;
          idx: number;
          fieldPath: string;
          errorMsg: string;
        }>,
      ) => {
        setOptions((prev) => {
          const newOptions = prev.map((item) => {
            const error = errors.find((e) => e.id === item.id);
            if (error) {
              return {
                ...item,
                status: ItemStatus.ERROR,
                errorMessage: error.errorMsg,
              };
            }
            // 清除不在错误列表中的项目的错误状态
            return item.status === ItemStatus.ERROR
              ? { ...item, status: ItemStatus.UNVISITED, errorMessage: undefined }
              : item;
          });
          return newOptions;
        });
      },
      [],
    );

    // 重置所有题目为未访问状态
    const resetToUnvisited = useCallback(() => {
      if (questionList && questionList.length > 0) {
        // 如果有 questionList，重新创建完整的 options
        const resetOptions = questionList.map((question) => ({
          id: question.questionId,
          status: ItemStatus.UNVISITED,
          errorMessage: undefined,
        }));
        setOptions(resetOptions);
        return;
      }
      // 如果没有 questionList，只重置现有的 options
      setOptions((prev) => {
        const newOptions = prev.map((item) => ({
          ...item,
          status: ItemStatus.UNVISITED,
          errorMessage: undefined,
        }));
        // 保存进度到本地
        localDraft.saveWithExpiry(paperId, newOptions, expiryTime);
        return newOptions;
      });
    }, [questionList]);

    // 删除题目
    const removeQuestion = useCallback(
      (id: string) => {
        setOptions((prev) => {
          const deletedIndex = prev.findIndex((item) => item.id === id);
          const newOptions = prev.filter((item) => item.id !== id);

          // 如果删除的不是当前选中的题目，直接返回
          if (deletedIndex !== activeIndex) {
            localDraft.saveWithExpiry(paperId, newOptions, expiryTime);
            return newOptions;
          }

          // 如果删除的是当前选中的题目，需要切换到前一个题目
          if (newOptions.length > 0) {
            // 计算新的选中索引：选择前一个题目，如果是第一个题目则选择下一个
            const newActiveIndex = deletedIndex > 0 ? deletedIndex - 1 : 0;

            // 使用 setTimeout 确保 options 状态更新完成后再执行后续操作
            setTimeout(() => {
              // 获取删除后的新选中题目选项
              const newActiveOption = newOptions[newActiveIndex];
              if (!newActiveOption) return;

              // 根据 ID 查找对应的 questionList 项，而不是使用索引
              const targetQuestion = questionList?.find((q) => q.questionId === newActiveOption.id);

              // 批量更新状态，避免多次渲染
              setActiveIndex(newActiveIndex);
              if (targetQuestion) {
                setActiveQuestion(targetQuestion);
              }
              updateQuestionStatus(newActiveOption.id, ItemStatus.ACTIVE);
            }, 0);

            // 由于 updateQuestionStatus 会保存，这里不需要重复保存
            return newOptions;
          }

          // 保存进度到本地（只有在不需要切换题目时才执行）
          localDraft.saveWithExpiry(paperId, newOptions, expiryTime);
          return newOptions;
        });
      },
      [
        activeIndex,
        paperId,
        localDraft,
        setActiveIndex,
        questionList,
        setActiveQuestion,
        updateQuestionStatus,
      ],
    );

    // 多题目切换
    const handleNumberSelectorChange = useCallback(
      async ({
        currentIdx,
        prevIdx,
      }: {
        currentVal?: string;
        currentIdx: number;
        prevVal?: string;
        prevIdx: number;
        options?: NumberSelectorOption[];
      }) => {
        const unvisitedItems: NumberSelectorOption[] = localDraft.loadWithExpiry(paperId) || [];
        const activeIdx = unvisitedItems.findIndex((item) => item.status === ItemStatus.ACTIVE);
        let prevIndex = prevIdx;

        if (activeIdx !== -1) {
          prevIndex = activeIdx;
        }
        // 获取当前最新的 options 状态
        const currentOptions = options || [];
        const currentOption = currentOptions[currentIdx];
        const prevOption = currentOptions[prevIndex];
        try {
          // 使用 validateFields 但捕获错误，不显示给用户
          await form?.validateFields();
          // 如果验证成功，清除之前题目的错误状态
          if (prevOption) {
            updateQuestionStatus(prevOption.id, ItemStatus.VISITED);
          }
        } catch (errorInfo) {
          // 如果验证失败，标记为错误状态
          if (prevOption) {
            updateQuestionStatus(prevOption.id, ItemStatus.ERROR, '表单验证失败');
          }
        }

        if (currentOption && currentOption.status !== ItemStatus.ACTIVE) {
          updateQuestionStatus(currentOption.id, ItemStatus.ACTIVE);
        }

        // 清空图片列表
        setAttachFileList([]);
        // 保存当前题目的更改
        handleBlurSave();
        // 等待所有保存操作完成
        await saveQueue.current;
        // 直接切换到目标题目
        setActiveIndex(currentIdx);
        // 设置当前活跃题目
        if (questionList && questionList[currentIdx]) {
          setActiveQuestion(questionList[currentIdx]);
        }
      },
      [
        options,
        form,
        updateQuestionStatus,
        saveQueue,
        handleBlurSave,
        setActiveIndex,
        questionList,
        setActiveQuestion,
        setAttachFileList,
        localDraft,
        paperId,
      ],
    );

    // 暴露方法给父组件 - 简化的 API
    useImperativeHandle(
      ref,
      () => ({
        setErrors,
        // getCurrentValue: () => activeIndex,
        getOptions: () => [...options],
        // getCount: () => options.length,
        resetToUnvisited,
        updateQuestionStatus,
        removeQuestion,
      }),
      [options, setErrors, resetToUnvisited, updateQuestionStatus, removeQuestion],
    );

    // 如果是单题模式，不显示选择器
    if (isSingleQuestion) {
      return null;
    }

    return (
      <NumberSelector
        ref={numberSelectorRef}
        options={options}
        value={activeIndex}
        onChange={handleNumberSelectorChange}
        browsingHistory={true}
      />
    );
  },
);

QuestionSegmented.displayName = 'QuestionSegmented';

export default memo(QuestionSegmented);
