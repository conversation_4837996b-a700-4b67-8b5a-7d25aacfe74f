import { showSubmitModal } from '@/components/SubmitModal';
import { fetchParseResult } from '@/services/api';
import { getAuditTaskDetail } from '@/services/api/review';
import { to } from 'await-to-js';
import { useCallback, useEffect } from 'react';
import { useQuestionCreate } from '../contexts/QuestionCreateContext';
import { QuestionCreateWay } from '../types';
// import mock from '../mock';

interface UseQuestionInitActionsProps {
  way?: QuestionCreateWay;
  data?: any;
}

/**
 * 题目初始化相关的业务逻辑 hooks
 */
export const useQuestionInitActions = ({ way, data }: UseQuestionInitActionsProps) => {
  const {
    paperId,
    setPaperId,
    setBaseInfo,
    activeIndex,
    questionList,
    setQuestionList,
    setIsLoading,
    setIsSingleQuestion,
    setActiveQuestion,
  } = useQuestionCreate();

  // // 监听 data.paperId 变化
  // useEffect(() => {
  //   if (data?.paperId) {
  //     setPaperId(data.paperId);
  //   }
  // }, [data?.paperId, setPaperId]);

  // 开始识别
  const identifyHandle = useCallback(
    (onConfirm?: () => void) => {
      if (questionList.length === 0) {
        setIsLoading(true);
        onConfirm?.();
        return;
      }
      showSubmitModal({
        title: '识别后，部分现有设置可能被覆盖',
        content: '',
        onOk: () => {
          setIsLoading(true);
          onConfirm?.();
        },
      });
    },
    [questionList.length, setIsLoading],
  );

  // 审核详情
  const fetchAuditTaskDetail = useCallback(async () => {
    setIsLoading(true);
    const [err, res] = await to(getAuditTaskDetail({ auditTaskId: data.auditTaskId }));
    if (err || res.code) {
      setIsLoading(false);
      return;
    }
    const newQuestionList = res.data.importQuestion?.list || [];
    setQuestionList(newQuestionList);
    setIsSingleQuestion(newQuestionList.length === 1);
    setPaperId(res.data.importQuestion?.paperId);
    // 直接设置当前活跃题目
    if (newQuestionList.length > 0) {
      setActiveQuestion(newQuestionList[activeIndex] || newQuestionList[0]);
    }
    setIsLoading(false);
  }, [
    data?.auditTaskId,
    setIsLoading,
    setQuestionList,
    setIsSingleQuestion,
    setActiveQuestion,
    activeIndex,
  ]);

  // 上传详情
  const fetchPaperResultDetail = useCallback(async () => {
    setIsLoading(true);
    const [err, res] = await to(fetchParseResult({ paperId: data?.paperId }));
    if (err || res.code) {
      setIsLoading(false);
      return;
    }
    // const res = mock;
    // console.log(res);
    setBaseInfo({
      phase: res.data.phase,
      subject: res.data.subject,
      generateChannel: res.data.generateChannel,
      paperName: res.data.paperName,
    });
    const newQuestionList = res.data.questionList || [];
    setQuestionList(newQuestionList);
    setIsSingleQuestion(newQuestionList.length === 1);
    // 直接设置当前活跃题目
    if (newQuestionList.length > 0) {
      setActiveQuestion(newQuestionList[activeIndex] || newQuestionList[0]);
    }
    setIsLoading(false);
  }, [
    paperId,
    data?.paperId,
    setIsLoading,
    setBaseInfo,
    setQuestionList,
    setIsSingleQuestion,
    setActiveQuestion,
    activeIndex,
  ]);

  // 重置数据
  const resetData = () => {
    setQuestionList([]);
    // 重置图片列表在 Context 中已经处理
  };

  const initQuestionList = useCallback(() => {
    resetData();
    if (way === 'upload') {
      if (!data?.paperId) return;
      setPaperId(data.paperId);
      fetchPaperResultDetail();
    }
    if (way === 'audit') {
      if (!data?.auditTaskId) return;
      fetchAuditTaskDetail();
    }
    if (way === 'correction' && data) {
      setQuestionList([data]);
      setIsSingleQuestion(true);
      setActiveQuestion(data);
    }
  }, [way, data?.auditTaskId, data?.paperId, data]);

  // 根据way类型，获取不同的数据
  useEffect(() => {
    initQuestionList();
  }, []);

  return {
    identifyHandle,
    initQuestionList,
    fetchAuditTaskDetail,
    fetchPaperResultDetail,
    resetData,
  };
};
