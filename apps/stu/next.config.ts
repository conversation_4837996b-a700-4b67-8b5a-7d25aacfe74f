import type { NextConfig } from "next";
import path from "path";
//    import { PHASE_PRODUCTION_BUILD } from "next/dist/shared/lib/constants";

const nextConfig: NextConfig = {
  productionBrowserSourceMaps: true,
  distDir: "dist",
  output: "standalone",
  assetPrefix:
    process.env.NODE_ENV === "production"
      ? "https://static.xiaoluxue.com/stu"
      : process.env.NODE_ENV === "test"
        ? "https://static.test.xiaoluxue.cn/stu"
        : "",
  transpilePackages: [
    "@repo/ui",
    "@repo/lib",
    "@repo/core",
    "@repo/rough-notation",
  ],
  experimental: {
    swcPlugins: [
      [
        "@preact-signals/safe-react/swc",
        {
          // you should use `auto` mode to track only components which uses `.value` access.
          // Can be useful to avoid tracking of server side components
          mode: "auto",
        } /* plugin options here */,
      ],
    ],
  },
  turbopack: {
    rules: {
      "*.svg": {
        loaders: [
          {
            loader: "@svgr/webpack",
            options: {
              icon: false,
            },
          },
        ],
        as: "*.js",
      },
    },
  },
  // 配置重写规则
  rewrites: async () => {
    return [
      {
        source: "/api/:path*",
        // destination: `https://m1.apifoxmock.com/m1/6405749-6102927-default/api/:path*`,
        destination: `/api/:path*`,
      },
    ];
  },
  outputFileTracingRoot: path.resolve(__dirname, "../../"),
  images: {
    remotePatterns: [
      {
        protocol: "https",
        hostname: "**.aliyuncs.com",
        port: "",
        search: "",
      },
      {
        protocol: "https",
        hostname: "**.xiaoluxue.cn",
        port: "",
        search: "",
      },
    ],
  },
  webpack(config) {
    config.module.rules.push({
      test: /\.svg$/,
      use: [
        {
          loader: "@svgr/webpack",
          options: {
            icon: false,
          },
        },
      ],
    });
    return config;
  },
};

export default nextConfig;
