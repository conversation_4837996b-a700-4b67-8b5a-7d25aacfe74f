import Rectangle from "@/public/icons/rectangle-bottom-2.svg";
import { useComputed } from "@preact-signals/safe-react";
import { formatTime } from "@repo/lib/utils/time";
import { Progress } from "@repo/ui/components/progress";
import { FC, useCallback, useEffect, useMemo, useRef, useState } from "react";
import { interpolate } from "remotion";
import { useVideoViewContext } from "../video-view-context";

type Size = {
  width: number;
  height: number;
  left: number;
  top: number;
};

// If a pane has been moved, it will cause a layout shift without
// the window having been resized. Those UI elements can call this API to
// force an update
const useElementSize = (
  ref: React.RefObject<HTMLElement | null>
): Size | null => {
  const [size, setSize] = useState<Size | null>(() => {
    if (!ref.current) {
      return null;
    }

    const rect = ref.current.getClientRects();
    if (!rect[0]) {
      return null;
    }

    return {
      width: rect[0].width as number,
      height: rect[0].height as number,
      left: rect[0].x as number,
      top: rect[0].y as number,
    };
  });

  const observer = useMemo(() => {
    if (typeof ResizeObserver === "undefined") {
      return null;
    }

    return new ResizeObserver((entries) => {
      if (!entries[0]) {
        return;
      }
      const { target } = entries[0];
      const newSize = target.getClientRects();

      if (!newSize?.[0]) {
        setSize(null);
        return;
      }

      const { width } = newSize[0];

      const { height } = newSize[0];

      setSize({
        width,
        height,
        left: newSize[0].x,
        top: newSize[0].y,
      });
    });
  }, []);

  const updateSize = useCallback(() => {
    if (!ref.current) {
      return;
    }

    const rect = ref.current.getClientRects();
    if (!rect[0]) {
      setSize(null);
      return;
    }

    setSize((prevState) => {
      if (!rect[0]) {
        return prevState;
      }

      const isSame =
        prevState &&
        prevState.width === rect[0].width &&
        prevState.height === rect[0].height &&
        prevState.left === rect[0].x &&
        prevState.top === rect[0].y;
      if (isSame) {
        return prevState;
      }

      return {
        width: rect[0].width as number,
        height: rect[0].height as number,
        left: rect[0].x as number,
        top: rect[0].y as number,
        windowSize: {
          height: window.innerHeight,
          width: window.innerWidth,
        },
      };
    });
  }, [ref]);

  useEffect(() => {
    if (!observer) {
      return;
    }

    const { current } = ref;
    if (current) {
      observer.observe(current);
    }

    return (): void => {
      if (current) {
        observer.unobserve(current);
      }
    };
  }, [observer, ref, updateSize]);

  useEffect(() => {
    window.addEventListener("resize", updateSize);

    return () => {
      window.removeEventListener("resize", updateSize);
    };
  }, [updateSize]);

  return useMemo(() => {
    if (!size) {
      return null;
    }

    return { ...size, refresh: updateSize };
  }, [size, updateSize]);
};

const calFrameFromX = (
  clientX: number,
  durationInFrames: number,
  width: number
) => {
  const pos = clientX;

  const frame = Math.round(
    interpolate(pos, [0, width], [0, Math.max(durationInFrames, 0)], {
      extrapolateLeft: "clamp",
      extrapolateRight: "clamp",
    })
  );
  console.log({ pos: pos / width, frame: frame / durationInFrames });
  return frame;
};

const findBodyInWhichDivIsLocated = (div: HTMLElement) => {
  let current = div;

  while (current.parentElement) {
    current = current.parentElement;
  }

  return current;
};

export const SeekBar: FC = () => {
  const { currentFrame, durationInFrames, fps, seekTo } = useVideoViewContext();
  // const isDragging = useSignal(false);
  const [isDragging, setIsDragging] = useState(false);
  const ref = useRef<HTMLDivElement>(null);
  const size = useElementSize(ref);
  const progress = useComputed(() => {
    return (currentFrame.value / durationInFrames) * 100;
  });

  const time = useComputed(() => {
    return `${partFrameToTime(currentFrame.value)} / ${partFrameToTime(
      durationInFrames
    )}`;
  });

  const width = size?.width ?? 0;
  const onPointerDown = useCallback(
    (e: React.TouchEvent<HTMLDivElement>) => {
      const touch = e.touches[0];
      if (!touch) {
        return;
      }
      setIsDragging(true);
      const posLeft = ref.current?.getBoundingClientRect().left;
      if (!posLeft) {
        return;
      }

      const _frame = calFrameFromX(
        touch.clientX - posLeft,
        durationInFrames,
        width
      );
      seekTo(_frame);
    },
    [durationInFrames, width, seekTo]
  );

  const onPointerMove = useCallback(
    (e: TouchEvent) => {
      if (!size) {
        return;
      }
      if (!isDragging) {
        return;
      }
      const touch = e.touches[0];
      if (!touch) {
        return;
      }
      console.log("onPointerMove");

      const posLeft = ref.current?.getBoundingClientRect().left;
      if (!posLeft) {
        return;
      }

      const _frame = calFrameFromX(
        touch.clientX - posLeft,
        durationInFrames,
        size.width
      );
      seekTo(_frame);
    },
    [isDragging, size, durationInFrames, seekTo]
  );

  const partFrameToTime = useCallback(
    (frame: number) => {
      return formatTime(Math.round(frame / fps));
    },
    [fps]
  );

  const onPointerUp = useCallback(() => {
    setIsDragging(false);
    if (!isDragging) {
      return;
    }
  }, [isDragging]);

  useEffect(() => {
    const body = findBodyInWhichDivIsLocated(ref.current as HTMLElement);

    body.addEventListener("touchend", onPointerUp, { capture: true });
    body.addEventListener("touchmove", onPointerMove);
    return () => {
      body.removeEventListener("touchend", onPointerUp);
      body.removeEventListener("touchmove", onPointerMove);
    };
  }, [isDragging, onPointerUp, onPointerMove]);

  return (
    <div className="flex h-12 w-full flex-row items-center justify-center gap-4 rounded-xl bg-white/80 px-5">
      <div className="relative flex-1">
        {isDragging && (
          <div
            className="absolute -top-20 flex flex-col items-center justify-end"
            style={{
              left: `${progress.value}%`,
              transform: "translateX(-50%)",
            }}
          >
            <div className="flex flex-row items-center justify-center rounded-lg bg-stone-900/60 px-3 py-2 text-center text-2xl font-normal text-white">
              {time.value}
            </div>
            <Rectangle className="fill-stone-900" />
          </div>
        )}
        <Progress
          ref={ref}
          onTouchStart={onPointerDown}
          className="h-2 w-full rounded-sm bg-zinc-800/20 *:rounded-sm *:bg-zinc-800/90"
          value={progress.value}
          max={100}
        />
      </div>
      <div className="text-xs font-medium leading-none text-zinc-800/90">
        {time.value}
      </div>
    </div>
  );
};
