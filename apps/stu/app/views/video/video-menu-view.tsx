import { IconButton } from "@/app/components/guide/guide-buttons";
import IconBack from "@/public/icons/back.svg";
import { FC } from "react";
import { useVideoViewContext } from "./video-view-context";

export const VideoMenuView: FC = () => {
  const { exit, showPlayerControls } = useVideoViewContext();

  if (!showPlayerControls) {
    return null;
  }

  return (
    <div className="absolute top-8 z-10 flex h-11 w-full flex-row items-center justify-between px-8">
      <IconButton icon={<IconBack />} onClick={exit} />
    </div>
  );
};
