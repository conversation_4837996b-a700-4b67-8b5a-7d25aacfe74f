import { FC } from "react";

import { WidgetViewProps } from "@/types/app/ui";
import { VideoControllerView } from "./video-controller-view";
import { VideoMenuView } from "./video-menu-view";
import { VideoPlayerView } from "./video-player-view";
import { VideoViewContextProvider } from "./video-view-context";

export const VideoWidgetView: FC<WidgetViewProps<"video">> = (props) => {
  return (
    <VideoViewContextProvider {...props}>
      <VideoPlayerView />
      <VideoMenuView />
      <VideoControllerView />
    </VideoViewContextProvider>
  );
};
