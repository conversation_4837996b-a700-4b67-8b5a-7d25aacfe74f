import {
  ContextMenu,
  ContextMenuItem,
} from "@/app/components/common/contextmenu";
import { notify } from "@/app/components/common/notify";
import { RightSidebar } from "@/app/components/common/right-sidebar";
import {
  DialogProps,
  DialogView,
} from "@/app/components/dialog/default-dialog";
import { GuideComment } from "@/app/components/guide/guide-comment";
import { GuideComments } from "@/app/components/guide/guide-comments";
import {
  useAddComment,
  useDeleteComment,
  useLikeComment,
  useReplyComment,
} from "@/app/models";
import { withResolvers } from "@/app/utils/withResolvers";
import CommentIcon from "@/public/icons/ic_tab_growth_default.svg";
import { useComputed, useSignal } from "@preact-signals/safe-react";
import { useEffect } from "@preact-signals/safe-react/react";
import { Reference } from "@repo/core/types/data/comment";
import { getAppInfo } from "@repo/core/utils/stu/device";
import { FC } from "react";
import { useCourseViewContext } from "../course/course-view-context";
import { useGuideViewContext } from "./guide-view-context";

const contextMenus: ContextMenuItem[] = [
  {
    icon: <CommentIcon className="size-[1.25em]" />,
    name: "评论",
  },
];
// 1=本班, 2=年级, 3=本校, 4=公开
const scopes = [
  {
    scope: "公开",
    value: 4,
  },
  {
    scope: "本校可见",
    value: 3,
  },
  {
    scope: "年级可见",
    value: 2,
  },
  {
    scope: "本班可见",
    value: 1,
  },
];

const objType = 100; // 100是课程
export const GuideCommentView: FC = () => {
  const {
    subjectId,
    lessonName,
    knowledgeName,
    hadShownRedirectComment,
    redirectCommentId,
    redirectCommentRootId,
    redirectReferenceId,
    studySessionId,
    studyType,
  } = useCourseViewContext();
  const {
    commentsBarVisible,
    commentInputVisible,
    ranges,
    commentRef,
    contextMenuPos,
    lessonId,
    knowledgeId,
    currentWidget,
    refreshReferences,
    markedRanges,
    markedReference,
    referenceList,
  } = useGuideViewContext();

  const comment = useSignal("");
  const scope = useSignal(4);
  const deleteDialogProps = useSignal<DialogProps>({});
  const reply = useSignal<{
    commentId: number;
    commentContent: string;
    p: ReturnType<typeof withResolvers>;
  } | null>(null);
  const replyRefP = useSignal<ReturnType<typeof withResolvers> | null>(null);
  const { addComment } = useAddComment();
  const { likeComment } = useLikeComment();
  const { replyComment } = useReplyComment();
  const { deleteComment } = useDeleteComment();

  const {
    value: { quote, referenceType, referenceImage },
  } = useComputed(() => {
    let referenceType = 0;
    let referenceImage = "";
    const quote = markedRanges.value.reduce((str, cur) => {
      let content = "";
      const doms = Array.from(
        document.querySelectorAll<HTMLSpanElement | HTMLImageElement>(
          `[data-line-id="${cur.lineId}"]`
        )
      );
      if (cur.type === "pic") {
        referenceType |= 2;
        referenceImage =
          doms.find((dom) => dom.dataset?.type === "pic")?.dataset?.content ??
          "";
        content = "[图片]";
      } else {
        referenceType |= 1;
        content = doms
          .map((dom) =>
            Boolean(dom.dataset?.charId) &&
            dom.dataset.textureId === cur.textureId &&
            Number(dom.dataset.charId) >= Number(cur.start) &&
            Number(dom.dataset.charId) <= Number(cur.end)
              ? dom.dataset.content
              : ""
          )
          .join("");
      }
      return `${str}${content}`;
    }, "");
    return { quote, referenceType, referenceImage };
  });

  const showContextMenu = ranges.length > 0;

  const handleSubmit = async () => {
    if (!currentWidget) return;
    const referenceId = Object.keys(
      markedReference.value?.referenceIds ?? {}
    )[0];
    const referencePositionMd5 = referenceId
      ? markedReference.value?.referenceIds[referenceId]
      : undefined;
    notify.show("发布中");
    if (reply.value) {
      const { commentId, p } = reply.value;
      const result = await replyComment({
        commentId,
        commentContent: comment.value,
        studySessionId,
        studyType,
        lessonId,
        widgetIndex: currentWidget.index,
        commentScope: scope.value,
      }).catch((e) => {
        notify.error("内容违规，请重新编辑");
        return Promise.reject(e);
      });
      p.resolve(result);
    } else {
      const appInfo = getAppInfo();
      const result = await addComment({
        knowledgeId,
        appVersion: appInfo?.versionName ?? "",
        lessonId,
        objId: lessonId,
        objType,
        commentContent: comment.value,
        commentScope: scope.value,
        referenceType,
        referenceContent: quote,
        referenceImage,
        referencePosition: { data: markedRanges.value },
        widgetIndex: currentWidget.index,
        widgetType: currentWidget.type,
        widgetName: currentWidget.name,
        subjectId: subjectId,
        courseName: lessonName,
        knowledgeName: knowledgeName,
        referenceId: referenceId ? Number(referenceId) : undefined,
        referencePositionMd5,
      }).catch((e) => {
        notify.error("内容违规，请重新编辑");
        return Promise.reject(e);
      });
      if (!referenceId) {
        refreshReferences();
      } else {
        replyRefP.value?.resolve(result);
      }
    }
    comment.value = "";
  };

  const handleLike = (commentId: number) => {
    return likeComment({
      commentId,
      objType,
      objId: lessonId,
      studySessionId,
      studyType,
      lessonId,
    });
  };

  const handleReply = (commentId: number, commentContent: string) => {
    const p = withResolvers();
    reply.value = { commentId, commentContent, p };
    commentInputVisible.value = true;
    return p.promise;
  };

  const handleDelete = async (commentId: number) => {
    const { promise, reject, resolve } = withResolvers();
    deleteDialogProps.value = {
      title: "确认删除该评论",
      open: true,
      onClose: reject,
      buttons: [
        {
          text: "取消",
          color: "white",
          onClick: reject,
        },
        {
          text: "确认",
          color: "red",
          onClick: () => resolve(true),
        },
      ],
    };
    try {
      await promise;
      await deleteComment({
        commentId,
      });
      // TODO)): Implement delete functionality
    } finally {
      deleteDialogProps.value = {};
    }
  };

  useEffect(() => {
    if (hadShownRedirectComment.value) return;
    if (redirectReferenceId) {
      const ref = referenceList?.find(
        (item) => item.referenceId === Number(redirectReferenceId)
      );
      if (!ref) return;
      const pos = ref?.referencePosition.data[0];
      const start = pos?.type === "text" ? Number(pos.start) : 0;
      const end = pos?.type === "text" ? Number(pos.end) : 0;
      markedReference.value = {
        start,
        end,
        commentCount: ref.commentCount ?? 0,
        isLast: false,
        referenceIds: {
          [ref.referenceId]: ref.referencePositionMd5,
        },
        referencePosition:
          (ref?.referencePosition
            .data as Reference["referencePosition"]["data"]) ?? [],
        referenceType: ref?.referenceType ?? 0,
      };
    }
  }, [
    hadShownRedirectComment.value,
    markedReference,
    redirectReferenceId,
    referenceList,
  ]);

  return (
    <>
      {props.a.b}
      {showContextMenu && (
        <ContextMenu
          menu={contextMenus}
          onItemClick={(item) => {
            if (item.name !== "评论") return;
            markedReference.value = null;
            markedRanges.value = ranges;
            commentInputVisible.value = true;
          }}
          port={commentRef}
          pos={contextMenuPos}
        />
      )}
      {commentInputVisible.value && (
        <GuideComment
          onSubmit={handleSubmit}
          scopes={scopes}
          comment={comment}
          scope={scope}
          quote={reply.value ? reply.value.commentContent : quote}
          reply={Boolean(reply.value)}
          onClose={() => {
            commentInputVisible.value = false;
            markedRanges.value = [];
            reply.value?.p.reject();
            reply.value = null;
            replyRefP.value?.reject();
            replyRefP.value = null;
          }}
        />
      )}
      <RightSidebar
        visible={commentsBarVisible.value}
        onClose={() => {
          if (commentInputVisible.value) return;
          markedReference.value = null;
          hadShownRedirectComment.value = true;
        }}
      >
        <GuideComments
          hadShownRedirectComment={hadShownRedirectComment.value}
          onRedirectedAction={() => {
            hadShownRedirectComment.value = true;
          }}
          redirectCommentId={redirectCommentId}
          redirectCommentRootId={redirectCommentRootId}
          objId={lessonId}
          referenceId={Object.keys(
            markedReference.value?.referenceIds ?? {}
          ).join(",")}
          onInputClickAction={() => {
            const p = withResolvers();
            replyRefP.value = p;
            markedRanges.value = markedReference.value?.referencePosition ?? [];
            commentInputVisible.value = true;
            return p.promise;
          }}
          inputValue={comment.value}
          onLikeAction={handleLike}
          onReplyAction={handleReply}
          onDeleteAction={handleDelete}
        />
      </RightSidebar>
      <DialogView {...deleteDialogProps.value} />
    </>
  );
};
