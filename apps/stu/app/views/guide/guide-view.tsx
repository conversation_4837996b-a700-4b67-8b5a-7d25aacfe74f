import { FC } from "react";

import { WidgetViewProps } from "@/types/app/ui";
import { GuideControllerView } from "./guide-controller-view";
import { GuideMenuView } from "./guide-menu-view";
import { GuidePlayerView } from "./guide-player-view";
import { GuideViewContextProvider } from "./guide-view-context";
import { GuideCommentView } from "./guide-comment-view";

export const GuideWidgetView: FC<WidgetViewProps<"guide">> = ({
  totalGuideCount,
  active,
  content,
}) => {
  return (
    <GuideViewContextProvider
      content={content}
      active={active}
      totalGuideCount={totalGuideCount}
    >
      <GuideMenuView />
      <GuidePlayerView />
      <GuideControllerView />
      <GuideCommentView />
    </GuideViewContextProvider>
  );
};
