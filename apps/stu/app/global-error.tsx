"use client";

import { But<PERSON> } from "@repo/ui/components/button";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@repo/ui/components/card";
import { useEffect, useState } from "react";

interface GlobalErrorProps {
  error: Error & { digest?: string };
  reset: () => void;
}

interface ErrorDetails {
  name: string;
  message: string;
  stack?: string;
  digest?: string;
  timestamp: string;
  userAgent: string;
  url: string;
  componentStack?: string;
}

export default function GlobalError({ error, reset }: GlobalErrorProps) {
  const [errorDetails, setErrorDetails] = useState<ErrorDetails | null>(null);
  const [showDetails, setShowDetails] = useState(false);
  const [copied, setCopied] = useState(false);

  useEffect(() => {
    // 收集错误详细信息
    const details: ErrorDetails = {
      name: error.name || "未知错误",
      message: error.message || "没有错误信息",
      stack: error.stack,
      digest: error.digest,
      timestamp: new Date().toISOString(),
      userAgent:
        typeof window !== "undefined" ? window.navigator.userAgent : "nextjs",
      url: typeof window !== "undefined" ? window.location.href : "nextjs",
    };

    setErrorDetails(details);

    // 记录错误到控制台
    console.error("全局捕获错误:", error);
    console.error("全局捕获错误详情:", details);

    // 监听未捕获的错误和Promise拒绝
    const handleUnhandledError = (event: ErrorEvent) => {
      console.error("Unhandled error:", event.error);
    };

    const handleUnhandledRejection = (event: PromiseRejectionEvent) => {
      console.error("Unhandled promise rejection:", event.reason);
    };

    window.addEventListener("error", handleUnhandledError);
    window.addEventListener("unhandledrejection", handleUnhandledRejection);

    return () => {
      window.removeEventListener("error", handleUnhandledError);
      window.removeEventListener(
        "unhandledrejection",
        handleUnhandledRejection
      );
    };
  }, [error]);

  const handleCopyError = async () => {
    if (!errorDetails) return;

    const errorText = `
错误详情:
名称: ${errorDetails.name}
消息: ${errorDetails.message}
时间: ${errorDetails.timestamp}
页面: ${errorDetails.url}
浏览器: ${errorDetails.userAgent}
${errorDetails.digest ? `错误摘要: ${errorDetails.digest}` : ""}
${errorDetails.stack ? `\n堆栈信息:\n${errorDetails.stack}` : ""}
    `.trim();

    try {
      await navigator.clipboard.writeText(errorText);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error("Failed to copy error details:", err);
    }
  };

  const handleReload = () => {
    window.location.reload();
  };

  const getErrorIcon = () => {
    if (
      error.message?.includes("ChunkLoadError") ||
      error.message?.includes("Loading chunk")
    ) {
      return "🔄";
    }
    if (
      error.message?.includes("Network") ||
      error.message?.includes("fetch")
    ) {
      return "🌐";
    }
    return "⚠️";
  };

  const getErrorTitle = () => {
    if (
      error.message?.includes("ChunkLoadError") ||
      error.message?.includes("Loading chunk")
    ) {
      return "应用需要更新";
    }
    if (
      error.message?.includes("Network") ||
      error.message?.includes("fetch")
    ) {
      return "网络连接异常";
    }
    return "应用发生错误";
  };

  const getErrorDescription = () => {
    if (
      error.message?.includes("ChunkLoadError") ||
      error.message?.includes("Loading chunk")
    ) {
      return "检测到新版本，请刷新页面以获取最新内容";
    }
    if (
      error.message?.includes("Network") ||
      error.message?.includes("fetch")
    ) {
      return "请检查网络连接后重试";
    }
    return "抱歉，应用遇到了意外错误，请尝试刷新页面";
  };

  return (
    <html>
      <body>
        <div className="flex min-h-screen items-center justify-center bg-gray-50 p-4">
          <Card className="w-full max-w-2xl">
            <CardHeader className="text-center">
              <div className="mb-4 text-6xl">{getErrorIcon()}</div>
              <CardTitle className="mb-2 text-2xl text-gray-900">
                {getErrorTitle()}
              </CardTitle>
              <p className="text-base text-gray-600">{getErrorDescription()}</p>
            </CardHeader>

            <CardContent className="space-y-6">
              {/* 主要操作按钮 */}
              <div className="flex flex-col justify-center gap-3 sm:flex-row">
                <Button
                  onClick={handleReload}
                  className="bg-orange-1 hover:bg-orange-1/90 px-6 py-2 text-white"
                >
                  刷新页面
                </Button>
                <Button onClick={reset} variant="outline" className="px-6 py-2">
                  重试
                </Button>
              </div>

              {/* 错误详情展开/收起 */}
              <div className="border-t pt-6">
                <Button
                  onClick={() => setShowDetails(!showDetails)}
                  variant="ghost"
                  className="w-full text-gray-600 hover:text-gray-800"
                >
                  {showDetails ? "隐藏" : "显示"}错误详情
                  <span className="ml-2">{showDetails ? "▲" : "▼"}</span>
                </Button>

                {showDetails && errorDetails && (
                  <div className="mt-4 space-y-4">
                    {/* 基本错误信息 */}
                    <div className="rounded-lg border border-red-200 bg-red-50 p-4">
                      <h3 className="mb-2 font-semibold text-red-800">
                        错误信息
                      </h3>
                      <div className="space-y-2 text-sm">
                        <div>
                          <span className="font-medium text-red-700">
                            类型:
                          </span>
                          <span className="ml-2 text-red-600">
                            {errorDetails.name}
                          </span>
                        </div>
                        <div>
                          <span className="font-medium text-red-700">
                            消息:
                          </span>
                          <span className="ml-2 text-red-600">
                            {errorDetails.message}
                          </span>
                        </div>
                        <div>
                          <span className="font-medium text-red-700">
                            时间:
                          </span>
                          <span className="ml-2 text-red-600">
                            {new Date(errorDetails.timestamp).toLocaleString(
                              "zh-CN"
                            )}
                          </span>
                        </div>
                        {errorDetails.digest && (
                          <div>
                            <span className="font-medium text-red-700">
                              错误ID:
                            </span>
                            <span className="ml-2 font-mono text-xs text-red-600">
                              {errorDetails.digest}
                            </span>
                          </div>
                        )}
                      </div>
                    </div>

                    {/* 环境信息 */}
                    <div className="rounded-lg border border-blue-200 bg-blue-50 p-4">
                      <h3 className="mb-2 font-semibold text-blue-800">
                        环境信息
                      </h3>
                      <div className="space-y-2 text-sm">
                        <div>
                          <span className="font-medium text-blue-700">
                            页面:
                          </span>
                          <span className="ml-2 break-all text-blue-600">
                            {errorDetails.url}
                          </span>
                        </div>
                        <div>
                          <span className="font-medium text-blue-700">
                            浏览器:
                          </span>
                          <span className="ml-2 text-xs text-blue-600">
                            {errorDetails.userAgent}
                          </span>
                        </div>
                      </div>
                    </div>

                    {/* 堆栈信息 */}
                    {errorDetails.stack && (
                      <div className="rounded-lg border border-gray-200 bg-gray-50 p-4">
                        <h3 className="mb-2 font-semibold text-gray-800">
                          堆栈跟踪
                        </h3>
                        <pre className="max-h-40 overflow-x-auto overflow-y-auto whitespace-pre-wrap rounded border bg-white p-3 text-xs text-gray-600">
                          {errorDetails.stack}
                        </pre>
                      </div>
                    )}

                    {/* 复制按钮 */}
                    <div className="flex justify-center">
                      <Button
                        onClick={handleCopyError}
                        variant="outline"
                        className="text-sm"
                        disabled={copied}
                      >
                        {copied ? "已复制!" : "复制错误信息"}
                      </Button>
                    </div>
                  </div>
                )}
              </div>

              {/* 帮助信息 */}
              <div className="border-t pt-6 text-center text-sm text-gray-500">
                <p>如果问题持续存在，请联系技术支持</p>
                <p className="mt-1">或尝试清除浏览器缓存后重新访问</p>
              </div>
            </CardContent>
          </Card>
        </div>
      </body>
    </html>
  );
}
