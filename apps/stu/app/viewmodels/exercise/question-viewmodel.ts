"use client";

import { FeedbackType, QUESTION_TYPE } from "@repo/core/enums";
import { HandleProgressParams, useProgressBar } from "@repo/core/exercise/components/ProgressBar";
import {
  ApiGetNextQuestionData,
  NextQuestionInfo,
  SubmitAnswer,
  SubmitAnswerResponse,
} from "@repo/core/exercise/model";
import {
  buildQuestionAnswerFromUserData,
  isChoiceQuestionType,
  isSelfEvaluationQuestionType,
} from "@repo/core/exercise/utils/question";
import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import {
  ANSWER_RESULT,
  type QuestionState,
  type QuestionViewModelState,
  type UserAnswerData,
  initialQuestionViewModelState,
} from "../../../types/app/exercise";
import {
  StudyType,
  useGetNextQuestion,
  useSubmitStudyAnswer,
} from "../../models/exercise";
import { surveillanceReport, trackEvent } from "../../utils/device";
import { useTransitionViewModel } from "./transition-viewmodel";
type QuestionViewModelProps = {
  studySessionId: number;
  studyType: StudyType;
  firstQuestionData?: ApiGetNextQuestionData;
  onComplete?: (totalTimeSpent?: number) => void;
  widgetIndex?: number; // AI 课中的组件序号
  activeInCourse?: boolean; // AI 课程页面激活状态
  transitionViewModel: ReturnType<typeof useTransitionViewModel>;
};

export function useQuestionViewModel({
  studySessionId,
  firstQuestionData,
  studyType,
  onComplete,
  widgetIndex,
  activeInCourse = true, // 默认为true，保持向后兼容
  transitionViewModel,
}: QuestionViewModelProps) {
  const [state, setState] = useState<QuestionViewModelState>(
    initialQuestionViewModelState
  );

  // 🔧 用于跟踪题目ID变化，避免意外重置用户状态
  const previousQuestionIdRef = useRef<string | null>(null);

  // 🔧 业务逻辑用的计时器（不触发渲染，用于准确的业务数据）
  const timeSpentRef = useRef<number>(0); // 毫秒

  // 🔧 AI课程：跟踪页面激活状态，用于清空时间
  const lastActiveRef = useRef(activeInCourse);

  // 🔧 计时器逻辑已移到 TimerDisplay 组件内部管理
  // ViewModel 只负责提供控制接口和获取时间数据

  // 🔥 使用 useProgressBar hook 统一管理进度条状态和动画
  const { progressBarProps, handleProgress, isAnimating } = useProgressBar({
    initialProgress: firstQuestionData?.progressInfo?.currentProgress || 0,
  });

  const { submitAnswer, isSubmitting } = useSubmitStudyAnswer();

  const { executeTransitionSequence } = transitionViewModel;

  // TODO: 等后续错题本上线了以后再打开 - 错题本相关hooks
  // const {
  //   toggle: toggleWrongQuestion,
  //   isLoading: isToggleWrongQuestionLoading,
  //   error: toggleWrongQuestionError
  // } = useToggleWrongQuestion();

  // 使用手动触发模式的useGetNextQuestion
  const nextQuestionApi = useGetNextQuestion({
    firstQuestionData,
    studyType: studyType,
    studySessionId: studySessionId,
  });

  // 🔧 新增：管理是否已经获取过初始题目
  const [hasInitialFetch, setHasInitialFetch] = useState(false);
  const [initialFetchedData, setInitialFetchedData] =
    useState<ApiGetNextQuestionData | null>(null);

  // 🔧 自动获取初始题目逻辑（只在没有firstQuestionData时执行一次）
  useEffect(() => {
    const needsInitialFetch =
      !firstQuestionData && !hasInitialFetch && studySessionId;
    console.log(`[QuestionViewModel] needsInitialFetch`, {
      needsInitialFetch,
      firstQuestionData,
      hasInitialFetch,
      studySessionId,
    });

    if (needsInitialFetch) {
      setHasInitialFetch(true); // 🔥 立即设置为true，绝不重置，避免无限循环

      // 🔥 直接调用API，避免函数依赖
      nextQuestionApi
        .trigger()
        .then((data) => {
          if (data?.hasNextQuestion) {
            setInitialFetchedData(data);
            handleProgress({
              type: "static",
              progress: data?.progressInfo?.currentProgress || 0,
            });
          } else {
            onComplete?.();
            console.warn(
              "[QuestionViewModel] 获取初始题目返回空数据，但不重试"
            );
            // 🔥 关键：即使返回空数据也不重置 hasInitialFetch，避免无限循环
          }
        })
        .catch((error) => {
          console.error("[QuestionViewModel] 获取初始题目失败:", error);
          // 🔥 关键：即使失败也不重置 hasInitialFetch，避免无限循环
          // 如果真的需要重试，应该通过其他机制（如用户主动刷新页面）
        });
    }
  }, [
    firstQuestionData,
    hasInitialFetch,
    studySessionId,
    nextQuestionApi,
    onComplete,
  ]); // 不包含任何函数依赖

  // 🔧 手动管理题目数据状态，避免 useMemo 的时序问题
  const [finalQuestionData, setFinalQuestionData] =
    useState<NextQuestionInfo | null>(null);
  const [currentQuestion, setCurrentQuestion] =
    useState<NextQuestionInfo | null>(null);

  // 🔥 新增：手动控制的下一题状态
  const [nextQuestionInfo, setNextQuestionInfo] =
    useState<NextQuestionInfo | null>(null);

  // 🔧 最终的题目数据：优先firstQuestionData，其次initialFetchedData
  const finalInitialQuestionInfo = firstQuestionData || initialFetchedData;
  console.log(`[QuestionViewModel] currentQuestion`, {
    currentQuestion,
    finalInitialQuestionInfo,
    finalQuestionData,
    nextQuestionInfo,
    firstQuestionData,
    initialFetchedData,
  });

  const handelQuestionProgress = useCallback(
    (
      result: SubmitAnswerResponse,
      questionState: QuestionState | "initial"
    ) => {
      // 更新进度,在已提交、放弃作答、首次错误时更新进度
      const canUpdateProgress = [
        "submitted",
        "giving_up",
        "first_attempt_incorrect",
        "initial",
      ].includes(questionState);
      if (!canUpdateProgress) return;
      // 🔧 关键：根据答题结果更新前端进度，首次错误只播放动画，不更新进度
      const currentProgress =
        questionState == "first_attempt_incorrect"
          ? progressBarProps.progress
          : result?.progressInfo?.currentProgress;

      // 🔥 方案三：检测是否为最后一题，如果是则立即设置进度条到100%
      const isLastQuestion = !result?.hasNextQuestion;

      // 🔥 判断答题结果，用于动画效果
      const isCorrect = result?.answerResult?.every(
        (item) => item.answerVerify === ANSWER_RESULT.CORRECT
      );

      // 🔥 生成爆炸文字
      const explosionText = result?.feedback?.content || "";

      // 🔥 计算进度百分比
      const progressPercentage = isLastQuestion
        ? 100 // 🔧 最后一题：直接设置为100%，避免时序竞争导致进度条未满
        : currentProgress; // 🔧 非最后一题：按比例递增进度，确保不超过100%

      let type: HandleProgressParams["type"] = isCorrect ? "single_correct" : "incorrect";
      if (questionState === "initial") {
        type = "static";
      }
      handleProgress({
        type: type,
        text: explosionText,
        progress: progressPercentage || 0,
        correctComboCount: result?.correctComboCount || 0,
        isAllCorrect: isLastQuestion && isCorrect,
      });

      return progressBarProps;
    },
    [handleProgress, progressBarProps]
  );

  // 🔧 简化的初始题目加载逻辑
  useEffect(() => {
    if (finalInitialQuestionInfo && !finalQuestionData) {
      setFinalQuestionData(finalInitialQuestionInfo.questionInfo || null);

      try {
        setCurrentQuestion(finalInitialQuestionInfo.questionInfo || null);
        previousQuestionIdRef.current = String(
          finalInitialQuestionInfo.questionInfo?.questionId
        );

        // 🔥 新增：如果有上次答题时长，恢复到计时器
        if (
          finalInitialQuestionInfo.lastAnswerDuration !== undefined &&
          finalInitialQuestionInfo.lastAnswerDuration > 0
        ) {
          timeSpentRef.current = finalInitialQuestionInfo.lastAnswerDuration;
        }
      } catch (error) {
        console.error("[QuestionViewModel] 初始题目转换失败:", error);
        setCurrentQuestion(null);
      }
    }
  }, [finalInitialQuestionInfo, finalQuestionData]);


  // 🔧 AI课程：处理页面激活状态变化，清空之前的计时
  useEffect(() => {
    if (
      studyType === StudyType.AI_COURSE &&
      activeInCourse &&
      !lastActiveRef.current
    ) {
      // 页面从未激活变为激活：清空计时器
      timeSpentRef.current = 0;
    }
    lastActiveRef.current = activeInCourse;
  }, [activeInCourse, studyType]);

  // 计算属性
  const computedValues = useMemo(() => {
    if (!currentQuestion) return { canRetryAnswer: false };

    const isChoiceQuestion = isChoiceQuestionType(
      currentQuestion.questionType || QUESTION_TYPE.QUESTION_TYPE_SINGLE_CHOICE
    );

    const isFirstAttemptIncorrect =
      state.questionState === "first_attempt_incorrect";
    const hasRetryAttempts = state.submitCount < 2;

    const canRetryAnswer =
      isChoiceQuestion && isFirstAttemptIncorrect && hasRetryAttempts;

    return { canRetryAnswer };
  }, [currentQuestion, state.questionState, state.submitCount]);

  const baseTrackParams = useMemo(() => {
    return {
      actual_duration_ms: timeSpentRef.current,
      study_session_id: studySessionId,
      study_type: studyType,
      question_id: currentQuestion?.questionId,
    };
  }, [studySessionId, studyType, currentQuestion?.questionId]);

  const trackEventWithFeedback = useCallback(
    (result: SubmitAnswerResponse, isGiveUp?: boolean) => {
      const isCorrect = result.answerResult?.every(
        (item) => item.answerVerify === ANSWER_RESULT.CORRECT
      );

      // 📊 埋点：放弃作答检测
      if (isGiveUp) {
        const giveUpTrackParams = {
          ...baseTrackParams,
          feedback_type: FeedbackType.GiveUp,
          answer_duration_ms: timeSpentRef.current,
          feedback_content: result.feedback.content,
        };
        trackEvent("give_up_feedback_show", giveUpTrackParams);
        surveillanceReport({
          operationType: 5,
          questionId: baseTrackParams.question_id,
          questionType: currentQuestion?.questionType ?? undefined,
          isCorrect: false,
          answerTime: timeSpentRef.current,
          correctStreak: result.correctComboCount,
          totalQuestions: result.progressInfo?.totalQuestions,
          accuracyRate: result.progressInfo?.currentProgress,
        });
      }

      // 答案正确/错误反馈
      const answerFeedbackParams = {
        ...baseTrackParams,
        feedback_type: isCorrect ? "answer_correct" : "answer_incorrect",
        feedback_content: result.feedback.content,
        answer_result: result.answerResult,
      };
      trackEvent("answer_feedback_show", answerFeedbackParams);
      surveillanceReport({
        operationType: 5,
        questionId: baseTrackParams.question_id,
        questionType: currentQuestion?.questionType ?? undefined,
        isCorrect,
        answerTime: timeSpentRef.current,
        correctStreak: result.correctComboCount,
        totalQuestions: result.progressInfo?.totalQuestions,
        accuracyRate: result.progressInfo?.currentProgress,
      });

      // 📊 埋点：反馈显示相关
      if (result.correctComboCount) {
        // 连胜反馈
        if (Number(result?.correctComboCount) > 1) {
          const streakFeedbackParams = {
            ...baseTrackParams,
            feedback_type: "winning_streak",
            streak_count: Number(result?.correctComboCount) || 0,
            feedback_content: result.feedback.content,
          };
          trackEvent("winning_streak_feedback_show", streakFeedbackParams);
        }
      }
    },
    [baseTrackParams, currentQuestion?.questionType]
  );

  const handleNextQuestion = useCallback((result: SubmitAnswerResponse) => {
    // 🔥 新增：如果有下一题，预处理并保存到 nextQuestion state
    if (result?.hasNextQuestion && result.nextQuestionInfo) {
      try {
        setNextQuestionInfo({
          ...result.nextQuestionInfo,
          hasNextQuestion: result.hasNextQuestion || false,
          isResume: result.nextQuestionInfo.isResume || false,
        });
      } catch (error) {
        console.error("[QuestionViewModel] 下一题预处理失败:", error);
        setNextQuestionInfo(null);
      }
    } else {
      setNextQuestionInfo(null);
    }
  }, []);

  const handleSubmitAnswer = useCallback(
    async (
      preparedAnswer: SubmitAnswer,
      skipSelfEvaluation = false
    ): Promise<SubmitAnswerResponse | null> => {
      // 🔥 防连点保护：如果正在提交中或进度条动画播放中，直接返回null
      if (isSubmitting || isAnimating) {
        console.warn(
          "[QuestionViewModel] 正在提交中或动画播放中，忽略重复提交请求",
          { isSubmitting, isAnimating }
        );
        return null;
      }

      if (!studySessionId || !currentQuestion) {
        console.error("[QuestionViewModel] 缺少必要参数", {
          hasStudySessionId: !!studySessionId,
          hasCurrentQuestion: !!currentQuestion,
        });
        throw new Error("缺少必要参数");
      }

      const newSubmitCount = state.submitCount + 1;

      try {
        const result = await submitAnswer({
          studySessionId: Number(studySessionId),
          questionId: currentQuestion.questionId,
          studentAnswers: preparedAnswer.studentAnswers,
          answerDuration: timeSpentRef.current,
          isGiveup: preparedAnswer.isGiveup, // 🔧 修复：传递放弃作答标识
          // 仅在 AI 课中传递 widgetIndex
          ...(studyType === StudyType.AI_COURSE &&
            widgetIndex !== undefined && { widgetIndex }),
        });

        // 处理下一题
        handleNextQuestion(result);

        // 📊 埋点：答案提交
        const answerTrackParams = {
          ...baseTrackParams,
          answer_content: preparedAnswer.studentAnswers,
          is_giveup: preparedAnswer.isGiveup || false,
          answer_duration: timeSpentRef.current,
        };
        trackEvent("exercise_answer_submit", answerTrackParams);

        setState((prev) => ({
          ...prev,
          lastSubmitResult: result,
          streakCount: result.correctComboCount || 0,
        }));

        const isChoiceQuestion = isChoiceQuestionType(
          currentQuestion.questionType ||
          QUESTION_TYPE.QUESTION_TYPE_SINGLE_CHOICE
        );

        const isGiveUp = preparedAnswer.isGiveup;

        // 🔧 首次错误的选择题不更新进度，保持原进度显示
        let questionState: QuestionState = "submitted";

        if (isGiveUp) {
          questionState = "giving_up";
          // 🔧 修复：放弃作答或未作答时保持 giving_up 状态，显示解析
          // 注意：不设置 setTimeout，让用户手动点击"继续"按钮
        } else if (isChoiceQuestion && newSubmitCount === 1) {
          const isIncorrect =
            !result.answerResult?.length ||
            result.answerResult?.[0]?.answerVerify === ANSWER_RESULT.INCORRECT;
          const isCorrect =
            result.answerResult?.[0]?.answerVerify === ANSWER_RESULT.CORRECT;
          if (isIncorrect) {
            questionState = "first_attempt_incorrect";
          } else if (isCorrect) {
            questionState = "submitted";
          }
          // 🔧 修复：首次答错时不重置计时器，只是停止计时，让用户重新选择
        } else {
          // 🆕 主观题两步提交逻辑
          const isSubjectiveQuestion = isSelfEvaluationQuestionType(
            currentQuestion.questionType ||
            QUESTION_TYPE.QUESTION_TYPE_SINGLE_CHOICE
          );
          const hasCompletedFirstStep =
            state.questionState === "answering" && isSubjectiveQuestion;
          const hasCompletedSecondStep =
            state.questionState === "awaiting_self_evaluation" &&
            isSubjectiveQuestion;

          if (
            hasCompletedFirstStep &&
            !preparedAnswer.studentAnswers.some(
              (answer) => answer.selfEvaluations
            )
          ) {
            // 🆕 检查是否应该跳过自评（空答案或放弃作答）
            if (skipSelfEvaluation || preparedAnswer.isGiveup) {
              questionState = "submitted";
            } else {
              questionState = "awaiting_self_evaluation";
              // 主观题第一步提交：没有自评数据，进入等待自评状态
            }
          } else if (hasCompletedSecondStep || !isSubjectiveQuestion) {
            // 主观题第二步提交或非主观题：设置为 submitted 状态
            questionState = "submitted";
          } else {
            questionState = "submitted";
            // 默认情况：设置为 submitted 状态
          }
        }
        setState((prev) => ({
          ...prev,
          questionState: questionState,
          submitCount: newSubmitCount,
        }));

        const answerCarelesslyFeedback = result.specialFeedbacks?.find(
          (item) => item.type == FeedbackType.AnswerCarelessly || item.type == FeedbackType.ContinuousCorrect
        );
        if (answerCarelesslyFeedback) {
          // 执行转场序列（只有在有下一题时才播放动画）
          executeTransitionSequence({
            currentQuestionId: currentQuestion.questionId,
            answerCount: result.answerCount,
            specialFeedbacks: [answerCarelesslyFeedback],
          });
        }
        // 处理埋点
        trackEventWithFeedback(result, isGiveUp);

        handelQuestionProgress(result, questionState);

        return result;
      } catch (error) {
        console.error("[QuestionViewModel] 提交答案失败:", error);
        throw error;
      }
    },
    [
      handelQuestionProgress,
      handleNextQuestion,
      trackEventWithFeedback,
      studyType,
      widgetIndex,
      studySessionId,
      currentQuestion,
      state.submitCount,
      state.questionState,
      submitAnswer,
      isSubmitting,
      isAnimating,
      baseTrackParams,
      executeTransitionSequence,
    ]
  );

  const handleContinue = useCallback(() => {
    console.log(` nextQuestionInfo1111111`, nextQuestionInfo);

    // 🔥 简化逻辑：直接检查 nextQuestion state
    if (nextQuestionInfo) {
      // 🔥 直接使用预处理好的数据，一步到位
      setFinalQuestionData(nextQuestionInfo);
      setCurrentQuestion(nextQuestionInfo || null);

      // 🔥 更新题目ID引用
      previousQuestionIdRef.current = String(nextQuestionInfo.questionId);

      // 🔥 重置计时器，并检查是否需要恢复时间
      if (
        nextQuestionInfo.lastAnswerDuration !== undefined &&
        nextQuestionInfo.lastAnswerDuration > 0
      ) {
        timeSpentRef.current = nextQuestionInfo.lastAnswerDuration;
      } else {
        timeSpentRef.current = 0; // 新题目重置为0
      }

      // 🔥 立即重置状态，不需要延迟
      setState((prev) => ({
        ...prev,
        questionState: "answering",
        submitCount: 0,
        lastSubmitResult: null, // 清除旧的提交结果
      }));

      // 🔥 清除下一题缓存
      setNextQuestionInfo(null);
    } else {
      if (onComplete) {
        onComplete();
      }
    }
  }, [nextQuestionInfo, onComplete]);

  const handleUncertainClick = useCallback(() => {
    // 点击"不确定"时，清空用户选择并设置状态为 uncertain
    setState((prev) => ({
      ...prev,
      questionState: "uncertain",
    }));
  }, []);

  const handleGiveUpClick = useCallback(async () => {
    if (!currentQuestion) {
      console.warn("[QuestionViewModel] Cannot give up: no current question");
      return;
    }

    // 构建放弃作答的答案数据
    const giveUpAnswer: SubmitAnswer = {
      questionId: currentQuestion.questionId,
      questionType:
        currentQuestion.questionType ||
        QUESTION_TYPE.QUESTION_TYPE_SINGLE_CHOICE,
      studentAnswers: [], // 空答案
      isGiveup: true, // 🔴 关键：标记为放弃作答
      answerTime: timeSpentRef.current, // 直接传递毫秒
    };

    try {
      // 调用提交答案方法
      await handleSubmitAnswer(giveUpAnswer);

      // 🔧 状态已经在 handleSubmitAnswer 中设置，不需要重复设置
    } catch (error) {
      console.error("[QuestionViewModel] 放弃作答提交失败:", error);
      // 如果提交失败，恢复到答题状态，让用户可以继续选择和提交
      // setState(prev => ({ ...prev, questionState: 'answering' }));
    }
  }, [currentQuestion, handleSubmitAnswer]);

  const handleSubmitClick = useCallback(
    async (
      userAnswerData?: UserAnswerData,
      selfEvaluations?: (0 | 1 | 2 | 3)[],
      skipSelfEvaluation = false
    ) => {
      if (!currentQuestion) {
        console.warn("[QuestionViewModel] Cannot submit: no current question");
        return;
      }

      // 🔧 修复：构建用户答案数据并调用提交逻辑
      try {
        // 根据题目类型构建答案数据
        const baseStudentAnswers = buildQuestionAnswerFromUserData(
          userAnswerData,
          currentQuestion.questionType ||
          QUESTION_TYPE.QUESTION_TYPE_SINGLE_CHOICE,
          currentQuestion.questionId
        );

        // 🆕 如果是主观题的第二步提交，添加自评数据
        const studentAnswers =
          selfEvaluations && baseStudentAnswers.length > 0
            ? baseStudentAnswers.map((answer, index) => ({
              ...answer,
              selfEvaluations:
                selfEvaluations[index] !== undefined
                  ? [selfEvaluations[index]]
                  : undefined,
            }))
            : baseStudentAnswers;

        const submitAnswer: SubmitAnswer = {
          questionId: currentQuestion.questionId,
          questionType:
            currentQuestion.questionType ||
            QUESTION_TYPE.QUESTION_TYPE_SINGLE_CHOICE,
          studentAnswers,
          isGiveup: false,
          answerTime: timeSpentRef.current,
        };

        // 调用提交答案方法
        await handleSubmitAnswer(submitAnswer, skipSelfEvaluation);
      } catch (error) {
        console.error("[QuestionViewModel] 提交失败:", error);
        // 如果提交失败，保持答题状态
        // setState(prev => ({ ...prev, questionState: 'answering' }));
      }
    },
    [currentQuestion, handleSubmitAnswer]
  );

  return {
    // 练习状态
    isResume: finalInitialQuestionInfo?.isResume ?? false,

    // 题目数据
    currentQuestion,
    questionData: finalQuestionData,

    // 数据状态
    isValidQuestionData: !!currentQuestion,
    isLoading: nextQuestionApi.isLoading,

    // 状态
    questionState: state.questionState,
    isSubmitting, // 提交状态
    submitCount: state.submitCount,
    streakCount: state.streakCount,
    lastSubmitResult: state.lastSubmitResult,
    isInWrongQuestionBank: state.isInWrongQuestionBank,

    // 计时器 - 🔧 修复：提供计时器控制接口，让 TimerDisplay 独立管理显示
    getTimeSpent: () => timeSpentRef.current, // 业务逻辑用（获取准确时间）
    timerControl: {
      isActive:
        currentQuestion &&
        (studyType === StudyType.AI_COURSE ? activeInCourse : true) && // 🔧 AI课程：只有页面激活时才开始计时
        (state.questionState === "answering" ||
          state.questionState === "uncertain" || // 🔧 新增：不确定状态下也要继续计时
          state.questionState === "first_attempt_incorrect"), // 🔧 修复：首次错误后仍需继续计时
      onTimeUpdate: (time: number) => {
        timeSpentRef.current = time; // 同步时间到业务逻辑
      },
      shouldReset: finalQuestionData?.questionId
        ? String(finalQuestionData.questionId)
        : "", // 题目ID变化时重置
      initialTime: finalQuestionData?.lastAnswerDuration || 0, // 🔧 修复：初始时间，用于恢复计时器
    },

    // 🔥 进度条相关状态和控制器
    progressBarProps,
    isProgressBarAnimating: isAnimating,

    // 计算属性
    canRetryAnswer: computedValues.canRetryAnswer,
    baseTrackParams,

    // 方法
    handleSubmitAnswer,
    handleContinue,
    handleUncertainClick,
    handleGiveUpClick,
    handleSubmitClick,
    setQuestionState: (newState: QuestionState) =>
      setState((prev) => ({ ...prev, questionState: newState })),

    fetchNextQuestion: nextQuestionApi.trigger,
  };
}
