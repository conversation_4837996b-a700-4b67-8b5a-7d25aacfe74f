import { cn } from "@repo/ui/lib/utils";
import { ComponentProps, FC, ReactNode } from "react";

export const IconButton: FC<{ icon: ReactNode } & ComponentProps<"button">> = ({
  icon,
  ...props
}) => {
  return (
    <button
      className="inline-flex size-10 items-center justify-center rounded-lg bg-white shadow-[0px_4px_16px_0px_rgba(35,42,64,0.05)] outline-[0.50px] outline-offset-[-0.50px] outline-zinc-800/10"
      {...props}
    >
      <div className="flex size-6 items-center justify-center">{icon}</div>
    </button>
  );
};

export const TranslucentGlassButton: FC<
  { icon?: ReactNode } & ComponentProps<"button">
> = ({ icon, children, className, ...props }) => {
  return (
    <button
      className={cn(
        "inline-flex h-11 items-center justify-center gap-1 rounded-xl bg-white px-3 opacity-80 shadow-[0px_4px_16px_0px_rgba(35,42,64,0.05)] outline-1 outline-offset-[-1px] outline-white",
        className
      )}
      {...props}
    >
      {icon && <div className="size-auto">{icon}</div>}
      {children && (
        <div className="text-base font-bold leading-tight text-zinc-800/90">
          {children}
        </div>
      )}
    </button>
  );
};

export const FloatTextButton: FC<
  { label: string; value: string } & ComponentProps<"button">
> = ({ label, value, ...props }) => {
  return (
    <button
      className="inline-flex h-8 w-max items-center justify-center gap-2 rounded-bl-3xl rounded-tl-3xl bg-gradient-to-l from-white to-[#FFFDFA] px-3 shadow-[-4px_0px_8px_0px_rgba(64,43,26,0.05)] outline-1 outline-offset-[-1px] outline-white"
      {...props}
    >
      <div className="text-xs font-medium leading-3">
        <span className="text-[#706E6B]">{label} </span>
        <span className="text-[#ADABA9]">{value}</span>
      </div>
    </button>
  );
};
