"use client";
import { useEffect, useMemo, useState } from "react";
import { getScreenSize } from "../utils/device";

function useScreen() {
  const deviceScreen = useMemo(() => getScreenSize(), []);
  const [screen, setScreen] = useState<{ width: number; height: number }>({
    width: 0,
    height: 0,
  });

  useEffect(() => {
    setScreen({
      width: deviceScreen?.width ?? window.innerWidth,
      height: deviceScreen?.height ?? window.innerHeight,
    });
  }, [deviceScreen]);

  useEffect(() => {
    const handleResize = () => {
      if (deviceScreen) {
        setScreen(deviceScreen);
      } else {
        setScreen({ width: window.innerWidth, height: window.innerHeight });
      }
    };
    window.addEventListener("resize", handleResize);
    return () => {
      window.removeEventListener("resize", handleResize);
    };
  }, [deviceScreen]);

  return screen;
}

export default useScreen;
