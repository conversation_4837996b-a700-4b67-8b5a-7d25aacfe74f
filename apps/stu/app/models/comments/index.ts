/**
 * 评论模块 - 统一导出
 */

// 导出 Model Hooks
export {
  useAddComment,
  useCommentsList,
  useDeleteComment,
  useLikeComment,
  useReplyComment,
  useSubCommentsList,
} from "./comments-model";

// 导出前端类型定义
export type {
  AddCommentPayload,
  Comment,
  CommentsListData,
  DeleteCommentPayload,
  ExtraInfo,
  GetCommentsParams,
  GetSubCommentsParams,
  LikeCommentPayload,
  PageInfo,
  PicReferenceCoordinate,
  ReferenceCoordinate,
  ReferencePosition,
  ReplyCommentPayload,
  TextReferenceCoordinate,
} from "./types";

// 导出后端数据校验 Schema
export {
  // 请求参数校验 Schema
  AddCommentPayloadSchema,
  ApiCommentSchema,
  ApiCommentsListDataSchema,
  ApiCommonResponseSchema,
  ApiExtraInfoSchema,
  ApiGetCommentsResponseSchema,
  ApiPageInfoSchema,
  ApiPicReferenceCoordinateSchema,
  ApiReferenceCoordinateSchema,
  ApiReferencePositionSchema,
  ApiTextReferenceCoordinateSchema,
  DeleteCommentPayloadSchema,
  LikeCommentPayloadSchema,
  ReplyCommentPayloadSchema,
} from "./schemas";

// 导出后端数据类型
export type {
  ApiComment,
  ApiCommentsListData,
  ApiCommonResponse,
  ApiExtraInfo,
  ApiGetCommentsResponse,
  ApiPageInfo,
  ApiPicReferenceCoordinate,
  ApiReferenceCoordinate,
  ApiReferencePosition,
  ApiTextReferenceCoordinate,
} from "./schemas";
