import { ApiGetNextQuestionData } from "@repo/core/exercise/model";
import { GuideWidgetData } from "@repo/core/types/data/widget-guide";
import { InteractiveWidgetData } from "@repo/core/types/data/widget-interactive";
import { VideoWidgetData } from "@repo/core/types/data/widget-video";

interface CourseSummary {
  lessonId: number;
  name: string;
  theme: string;
  total: number;
  currentWidgetIndex: number;
  widgets: CourseWidgetSummary[];
  nextQuestionParams: string;
  lessonVersion: string;
}

interface CourseWidgetSummary {
  index: number;
  name: string;
  type: WidgetType;
  status: WidgetStatus;
  cdnUrl?: string;
}

type WidgetStatus = "locked" | "completed" | "unlocked";

type WidgetDataMap = {
  guide: GuideWidgetData;
  exercise: ApiGetNextQuestionData;
  video: VideoWidgetData;
  interactive: InteractiveWidgetData;
};

interface CourseWidget<T extends WidgetType = WidgetType> {
  index: number;
  name: string;
  type: T;
  data: WidgetDataMap[T];
}

type WidgetType = "guide" | "exercise" | "video" | "interactive";

interface GuideProgress {
  frame: number; // 上一次播放的帧
}

export type {
  CourseSummary,
  CourseWidget,
  CourseWidgetSummary,
  GuideProgress,
  WidgetStatus,
  WidgetType,
};
