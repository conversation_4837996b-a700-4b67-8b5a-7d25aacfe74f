import { GuideWidgetData } from "@repo/core/types/data/widget-guide";
import { InteractiveWidgetData } from "@repo/core/types/data/widget-interactive";
import { VideoWidgetData } from "@repo/core/types/data/widget-video";

export interface ApiLessonSummary {
  /**
   * 课程ID
   */
  lessonId: number;
  /**
   * 课程名称
   */
  lessonName: string;
  /**
   * 前端地址
   */
  lessonWebUrl: string;
  /**
   * 课程组件列表
   */
  lessonWidgets: ApiLessonWidgetSummary[];
  /**
   * 主题
   */
  theme: string;
  /**
   * 总课程数
   */
  totalWidgetNum: number;
  /**
   * 当前组件下标
   */
  currentWidgetIndex: number;
  /**
   * 下一题接口用的的参数拼接
   */
  nextQuestionParams: string;
  /**
   * 课程版本
   */
  lessonVersion: string;
}

export interface ApiLessonWidgetSummary {
  /**
   * 组件下标
   */
  widgetIndex: number;
  /**
   * 组件名称
   */
  widgetName: string;
  /**
   * 组件类型
   */
  widgetType: string;
  /**
   * 组件状态
   */
  status: string;
  /**
   * 组件CDN地址
   */
  cdnUrl?: string;
}

export interface ApiLessonWidget extends ApiLessonWidgetSummary {
  /**
   * 文稿组件是videoJson  视频组件是视频信息
   */
  data: GuideWidgetData | InteractiveWidgetData | VideoWidgetData;
}
